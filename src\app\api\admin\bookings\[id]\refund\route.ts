import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getAuthTokenFromRequest } from '@/utils/auth';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;

    // Verify admin authentication
    const authToken = getAuthTokenFromRequest(request);
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const [, accountType] = authToken.split('_');
    if (accountType !== 'admin') {
      return NextResponse.json({ 
        error: 'Unauthorized - Admin access required' 
      }, { status: 403 });
    }

    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ 
        error: 'Invalid booking ID' 
      }, { status: 400 });
    }

    // Get the refund reason from request body
    const body = await request.json();
    const { reason = 'Admin initiated refund' } = body;

    // Get booking details
    const bookingResult = await query(`
      SELECT * FROM service_bookings WHERE id = ?
    `, [id]) as any[];

    if (!bookingResult || bookingResult.length === 0) {
      return NextResponse.json({ 
        error: 'Booking not found' 
      }, { status: 404 });
    }

    const booking = bookingResult[0];

    // Check if booking is eligible for refund
    if (booking.payment_status === 'refunded') {
      return NextResponse.json({ 
        error: 'Booking has already been refunded' 
      }, { status: 400 });
    }

    if (booking.payment_status !== 'paid') {
      return NextResponse.json({ 
        error: 'Booking payment status does not allow refund' 
      }, { status: 400 });
    }

    // Process the refund
    try {
      // Update booking payment status to refunded
      await query(`
        UPDATE service_bookings 
        SET payment_status = 'refunded', 
            status = 'cancelled',
            updated_at = NOW() 
        WHERE id = ?
      `, [id]);

      // Update any related payment transactions
      await query(`
        UPDATE payment_transactions 
        SET status = 'refunded', updated_at = NOW() 
        WHERE booking_id = ? AND status = 'succeeded'
      `, [id]);

      // Create a refund record for tracking
      await query(`
        INSERT INTO refunds (booking_id, amount, reason, status, created_at)
        VALUES (?, ?, ?, 'processed', NOW())
      `, [id, booking.price, reason]);

      return NextResponse.json({
        success: true,
        message: 'Refund processed successfully',
        refund: {
          booking_id: id,
          amount: booking.price,
          reason: reason,
          status: 'processed'
        }
      });

    } catch (refundError) {
      console.error('Refund processing error:', refundError);
      return NextResponse.json({
        error: 'Failed to process refund',
        details: refundError instanceof Error ? refundError.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Admin refund error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET endpoint to check refund eligibility
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;

    // Verify admin authentication
    const authToken = getAuthTokenFromRequest(request);
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const [, accountType] = authToken.split('_');
    if (accountType !== 'admin') {
      return NextResponse.json({ 
        error: 'Unauthorized - Admin access required' 
      }, { status: 403 });
    }

    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ 
        error: 'Invalid booking ID' 
      }, { status: 400 });
    }

    // Get booking details
    const bookingResult = await query(`
      SELECT id, status, payment_status, price, payment_method
      FROM service_bookings WHERE id = ?
    `, [id]) as any[];

    if (!bookingResult || bookingResult.length === 0) {
      return NextResponse.json({ 
        error: 'Booking not found' 
      }, { status: 404 });
    }

    const booking = bookingResult[0];

    // Check refund eligibility
    const isEligible = booking.payment_status === 'paid' && booking.payment_status !== 'refunded';
    const alreadyRefunded = booking.payment_status === 'refunded';

    return NextResponse.json({
      success: true,
      booking: {
        id: booking.id,
        status: booking.status,
        payment_status: booking.payment_status,
        amount: booking.price,
        payment_method: booking.payment_method
      },
      refund_eligible: isEligible,
      already_refunded: alreadyRefunded,
      message: alreadyRefunded 
        ? 'This booking has already been refunded'
        : isEligible 
          ? 'This booking is eligible for refund'
          : 'This booking is not eligible for refund'
    });

  } catch (error) {
    console.error('Refund eligibility check error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
